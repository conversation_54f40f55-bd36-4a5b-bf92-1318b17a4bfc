@echo off
echo 正在设置MinGW-w64开发环境...
echo.

REM 创建开发目录
if not exist "C:\dev" mkdir "C:\dev"
cd /d "C:\dev"

echo 请手动下载以下文件：
echo.
echo 1. MinGW-w64: https://github.com/niXman/mingw-builds-binaries/releases
echo    下载: x86_64-*-release-win32-seh-ucrt-*.7z
echo.
echo 2. OpenCV: https://opencv.org/releases/
echo    下载: opencv-*-windows.exe
echo.
echo 下载完成后：
echo 1. 解压MinGW到 C:\dev\mingw64
echo 2. 解压OpenCV到 C:\dev\opencv
echo 3. 运行 setup_env.bat 设置环境变量
echo.
pause
