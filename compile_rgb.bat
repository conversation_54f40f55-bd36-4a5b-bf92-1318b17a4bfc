@echo off
echo 编译RGB图像处理程序...

REM 设置环境变量
call setup_env.bat

REM 编译命令
echo 正在编译 rgb.cpp...
g++ -std=c++11 rgb.cpp -o rgb.exe ^
    -IC:\dev\opencv\build\include ^
    -LC:\dev\opencv\build\x64\mingw\lib ^
    -lopencv_core -lopencv_imgproc -lopencv_imgcodecs -lopencv_highgui

if %ERRORLEVEL% == 0 (
    echo 编译成功！
    echo 运行程序: rgb.exe
    echo.
    rgb.exe
) else (
    echo 编译失败，请检查OpenCV安装和路径设置
)

pause
