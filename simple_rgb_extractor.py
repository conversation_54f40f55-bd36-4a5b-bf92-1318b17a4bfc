"""
简单的RGB图像处理程序 - 只使用Python内置库
需要安装Pillow库: pip install Pillow
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import os

    def calculate_histogram(channel_data):
        """计算单个通道的直方图数据"""
        histogram = [0] * 256
        for pixel in channel_data:
            histogram[pixel] += 1
        return histogram

    def draw_histogram(histogram, color, title, width=800, height=400):
        """绘制美观的单个通道直方图"""
        # 创建画布，使用渐变背景
        img = Image.new('RGB', (width, height), (248, 249, 250))
        draw = ImageDraw.Draw(img)

        # 绘制背景网格
        grid_color = (230, 230, 230)
        margin_left, margin_right = 60, 40
        margin_top, margin_bottom = 60, 80
        chart_width = width - margin_left - margin_right
        chart_height = height - margin_top - margin_bottom

        # 绘制水平网格线
        for i in range(6):
            y = margin_top + (chart_height * i // 5)
            draw.line([(margin_left, y), (width - margin_right, y)], fill=grid_color, width=1)

        # 绘制垂直网格线
        for i in range(0, 256, 32):
            x = margin_left + (chart_width * i // 255)
            draw.line([(x, margin_top), (x, height - margin_bottom)], fill=grid_color, width=1)

        # 找到最大值用于归一化
        max_value = max(histogram) if max(histogram) > 0 else 1

        # 绘制直方图柱子
        bar_width = chart_width / 256
        for i, value in enumerate(histogram):
            if value > 0:  # 只绘制有数据的柱子
                # 计算柱子高度
                bar_height = int((value / max_value) * chart_height)

                # 计算位置
                x1 = margin_left + int(i * bar_width)
                x2 = margin_left + int((i + 1) * bar_width)
                y1 = height - margin_bottom
                y2 = height - margin_bottom - bar_height

                # 创建渐变效果的颜色
                if color == (255, 0, 0):  # 红色
                    fill_color = (min(255, color[0] - 30), color[1], color[2])
                elif color == (0, 255, 0):  # 绿色
                    fill_color = (color[0], min(255, color[1] - 30), color[2])
                else:  # 蓝色
                    fill_color = (color[0], color[1], min(255, color[2] - 30))

                # 绘制柱子
                draw.rectangle([x1, y2, x2, y1], fill=fill_color, outline=color, width=1)

        # 绘制坐标轴
        axis_color = (100, 100, 100)
        # X轴
        draw.line([(margin_left, height - margin_bottom), (width - margin_right, height - margin_bottom)],
                 fill=axis_color, width=2)
        # Y轴
        draw.line([(margin_left, margin_top), (margin_left, height - margin_bottom)],
                 fill=axis_color, width=2)

        # 添加标题
        try:
            font_title = ImageFont.load_default()
            title_bbox = draw.textbbox((0, 0), title, font=font_title)
            title_width = title_bbox[2] - title_bbox[0]
            title_x = (width - title_width) // 2
            draw.text((title_x, 15), title, fill=(50, 50, 50), font=font_title)
        except:
            draw.text((width//2 - 50, 15), title, fill=(50, 50, 50))

        # 添加坐标轴标签
        try:
            font_label = ImageFont.load_default()
            # X轴标签
            draw.text((margin_left - 5, height - margin_bottom + 10), '0', fill=axis_color, font=font_label)
            draw.text((width - margin_right - 15, height - margin_bottom + 10), '255', fill=axis_color, font=font_label)
            draw.text((width//2 - 20, height - margin_bottom + 30), 'Pixel Value', fill=axis_color, font=font_label)

            # Y轴标签
            draw.text((10, margin_top - 5), str(max_value), fill=axis_color, font=font_label)
            draw.text((10, height - margin_bottom - 10), '0', fill=axis_color, font=font_label)

            # 添加中间刻度
            for i in range(1, 5):
                y_val = int(max_value * i / 5)
                y_pos = height - margin_bottom - (chart_height * i // 5)
                draw.text((10, y_pos - 5), str(y_val), fill=axis_color, font=font_label)

        except:
            # 简化版标签
            draw.text((margin_left - 5, height - margin_bottom + 10), '0', fill=axis_color)
            draw.text((width - margin_right - 15, height - margin_bottom + 10), '255', fill=axis_color)

        return img

    def create_enhanced_histogram(r_histogram, g_histogram, b_histogram):
        """创建增强版的美观合并直方图"""
        width, height = 1200, 600

        # 创建带渐变背景的画布
        img = Image.new('RGBA', (width, height))

        # 创建渐变背景
        for y in range(height):
            # 从浅灰到白色的渐变
            gray_value = int(240 + (15 * y / height))
            for x in range(width):
                img.putpixel((x, y), (gray_value, gray_value, gray_value + 5, 255))

        draw = ImageDraw.Draw(img)

        # 设置边距和图表区域
        margin_left, margin_right = 100, 80
        margin_top, margin_bottom = 120, 120
        chart_width = width - margin_left - margin_right
        chart_height = height - margin_top - margin_bottom

        # 绘制精美的网格
        grid_color = (200, 200, 200, 180)
        # 水平网格线
        for i in range(6):
            y = margin_top + (chart_height * i // 5)
            draw.line([(margin_left, y), (width - margin_right, y)], fill=grid_color, width=1)

        # 垂直网格线
        for i in range(0, 256, 32):
            x = margin_left + (chart_width * i // 255)
            draw.line([(x, margin_top), (x, height - margin_bottom)], fill=grid_color, width=1)

        # 找到全局最大值
        max_r = max(r_histogram) if max(r_histogram) > 0 else 1
        max_g = max(g_histogram) if max(g_histogram) > 0 else 1
        max_b = max(b_histogram) if max(b_histogram) > 0 else 1
        global_max = max(max_r, max_g, max_b)

        # 绘制填充区域（半透明）
        bar_width = chart_width / 256
        alpha = 100  # 更透明的效果

        # 创建三个透明图层
        r_layer = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        g_layer = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        b_layer = Image.new('RGBA', (width, height), (0, 0, 0, 0))

        r_draw = ImageDraw.Draw(r_layer)
        g_draw = ImageDraw.Draw(g_layer)
        b_draw = ImageDraw.Draw(b_layer)

        # 绘制柱状图
        for i in range(256):
            x1 = margin_left + int(i * bar_width)
            x2 = margin_left + int((i + 1) * bar_width) - 1
            y_base = height - margin_bottom

            # 红色通道
            if r_histogram[i] > 0:
                r_height = int((r_histogram[i] / global_max) * chart_height)
                y_top = y_base - r_height
                r_draw.rectangle([x1, y_top, x2, y_base], fill=(255, 100, 100, alpha))

            # 绿色通道
            if g_histogram[i] > 0:
                g_height = int((g_histogram[i] / global_max) * chart_height)
                y_top = y_base - g_height
                g_draw.rectangle([x1, y_top, x2, y_base], fill=(100, 255, 100, alpha))

            # 蓝色通道
            if b_histogram[i] > 0:
                b_height = int((b_histogram[i] / global_max) * chart_height)
                y_top = y_base - b_height
                b_draw.rectangle([x1, y_top, x2, y_base], fill=(100, 100, 255, alpha))

        # 合并图层
        img = Image.alpha_composite(img, r_layer)
        img = Image.alpha_composite(img, g_layer)
        img = Image.alpha_composite(img, b_layer)

        # 转换为RGB模式以便绘制线条
        final_img = img.convert('RGB')
        final_draw = ImageDraw.Draw(final_img)

        # 绘制平滑的轮廓线
        def draw_smooth_line(histogram, color, line_width=3):
            points = []
            for i in range(256):
                x = margin_left + int(i * bar_width)
                y = height - margin_bottom - int((histogram[i] / global_max) * chart_height)
                points.append((x, y))

            # 绘制平滑线条
            for i in range(len(points) - 1):
                final_draw.line([points[i], points[i + 1]], fill=color, width=line_width)

        # 绘制三条轮廓线
        draw_smooth_line(r_histogram, (220, 50, 50), 3)
        draw_smooth_line(g_histogram, (50, 180, 50), 3)
        draw_smooth_line(b_histogram, (50, 50, 220), 3)

        # 添加标题和装饰
        try:
            font = ImageFont.load_default()

            # 主标题
            title = "RGB Channels Histogram Analysis"
            title_bbox = final_draw.textbbox((0, 0), title, font=font)
            title_width = title_bbox[2] - title_bbox[0]
            title_x = (width - title_width) // 2
            final_draw.text((title_x, 30), title, fill=(40, 40, 40), font=font)

            # 副标题
            subtitle = "Enhanced visualization with transparency and smooth curves"
            subtitle_bbox = final_draw.textbbox((0, 0), subtitle, font=font)
            subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
            subtitle_x = (width - subtitle_width) // 2
            final_draw.text((subtitle_x, 55), subtitle, fill=(100, 100, 100), font=font)

            # 图例
            legend_y = 80
            final_draw.rectangle([margin_left, legend_y, margin_left + 20, legend_y + 15], fill=(220, 50, 50))
            final_draw.text((margin_left + 30, legend_y), "Red Channel", fill=(220, 50, 50), font=font)

            final_draw.rectangle([margin_left + 150, legend_y, margin_left + 170, legend_y + 15], fill=(50, 180, 50))
            final_draw.text((margin_left + 180, legend_y), "Green Channel", fill=(50, 180, 50), font=font)

            final_draw.rectangle([margin_left + 320, legend_y, margin_left + 340, legend_y + 15], fill=(50, 50, 220))
            final_draw.text((margin_left + 350, legend_y), "Blue Channel", fill=(50, 50, 220), font=font)

            # 坐标轴标签
            axis_color = (80, 80, 80)
            final_draw.text((margin_left - 5, height - margin_bottom + 10), '0', fill=axis_color, font=font)
            final_draw.text((width - margin_right - 15, height - margin_bottom + 10), '255', fill=axis_color, font=font)
            final_draw.text((width // 2 - 20, height - margin_bottom + 10), 'Pixel Intensity', fill=axis_color, font=font)

            # Y轴标签
            final_draw.text((15, margin_top - 5), str(global_max), fill=axis_color, font=font)
            final_draw.text((15, height - margin_bottom - 5), '0', fill=axis_color, font=font)

            # 中间刻度
            for i in range(1, 5):
                y_val = int(global_max * i / 5)
                y_pos = height - margin_bottom - (chart_height * i // 5)
                final_draw.text((15, y_pos - 5), str(y_val), fill=axis_color, font=font)

        except:
            # 简化版标签
            final_draw.text((width//2 - 150, 30), "RGB Channels Histogram Analysis", fill=(40, 40, 40))
            final_draw.text((margin_left, 80), "Red", fill=(220, 50, 50))
            final_draw.text((margin_left + 80, 80), "Green", fill=(50, 180, 50))
            final_draw.text((margin_left + 160, 80), "Blue", fill=(50, 50, 220))

        return final_img

    def create_image_with_histogram(original_image, r_histogram, g_histogram, b_histogram):
        """创建原图与直方图的合并显示"""

        # 调整原图大小以适应布局
        img_width, img_height = original_image.size

        # 计算合适的显示尺寸
        max_img_width = 600
        max_img_height = 400

        # 保持宽高比缩放
        scale = min(max_img_width / img_width, max_img_height / img_height)
        new_img_width = int(img_width * scale)
        new_img_height = int(img_height * scale)

        resized_image = original_image.resize((new_img_width, new_img_height), Image.Resampling.LANCZOS)

        # 创建直方图
        hist_width, hist_height = 600, 300

        # 创建直方图画布
        hist_img = Image.new('RGBA', (hist_width, hist_height))

        # 创建渐变背景
        for y in range(hist_height):
            gray_value = int(245 + (10 * y / hist_height))
            for x in range(hist_width):
                hist_img.putpixel((x, y), (gray_value, gray_value, gray_value + 3, 255))

        hist_draw = ImageDraw.Draw(hist_img)

        # 设置直方图边距
        h_margin_left, h_margin_right = 60, 40
        h_margin_top, h_margin_bottom = 40, 60
        h_chart_width = hist_width - h_margin_left - h_margin_right
        h_chart_height = hist_height - h_margin_top - h_margin_bottom

        # 绘制网格
        grid_color = (210, 210, 210, 150)
        for i in range(5):
            y = h_margin_top + (h_chart_height * i // 4)
            hist_draw.line([(h_margin_left, y), (hist_width - h_margin_right, y)], fill=grid_color, width=1)

        for i in range(0, 256, 64):
            x = h_margin_left + (h_chart_width * i // 255)
            hist_draw.line([(x, h_margin_top), (x, hist_height - h_margin_bottom)], fill=grid_color, width=1)

        # 计算全局最大值
        max_r = max(r_histogram) if max(r_histogram) > 0 else 1
        max_g = max(g_histogram) if max(g_histogram) > 0 else 1
        max_b = max(b_histogram) if max(b_histogram) > 0 else 1
        global_max = max(max_r, max_g, max_b)

        # 绘制半透明柱状图
        bar_width = h_chart_width / 256
        alpha = 80

        for i in range(256):
            x1 = h_margin_left + int(i * bar_width)
            x2 = h_margin_left + int((i + 1) * bar_width) - 1
            y_base = hist_height - h_margin_bottom

            # 红色通道
            if r_histogram[i] > 0:
                r_height = int((r_histogram[i] / global_max) * h_chart_height)
                y_top = y_base - r_height
                hist_draw.rectangle([x1, y_top, x2, y_base], fill=(255, 120, 120, alpha))

            # 绿色通道
            if g_histogram[i] > 0:
                g_height = int((g_histogram[i] / global_max) * h_chart_height)
                y_top = y_base - g_height
                hist_draw.rectangle([x1, y_top, x2, y_base], fill=(120, 255, 120, alpha))

            # 蓝色通道
            if b_histogram[i] > 0:
                b_height = int((b_histogram[i] / global_max) * h_chart_height)
                y_top = y_base - b_height
                hist_draw.rectangle([x1, y_top, x2, y_base], fill=(120, 120, 255, alpha))

        # 转换为RGB并绘制轮廓线
        hist_final = hist_img.convert('RGB')
        hist_final_draw = ImageDraw.Draw(hist_final)

        # 绘制轮廓线
        def draw_histogram_line(histogram, color):
            points = []
            for i in range(256):
                x = h_margin_left + int(i * bar_width)
                y = hist_height - h_margin_bottom - int((histogram[i] / global_max) * h_chart_height)
                points.append((x, y))

            for i in range(len(points) - 1):
                hist_final_draw.line([points[i], points[i + 1]], fill=color, width=2)

        draw_histogram_line(r_histogram, (200, 60, 60))
        draw_histogram_line(g_histogram, (60, 160, 60))
        draw_histogram_line(b_histogram, (60, 60, 200))

        # 添加直方图标签
        try:
            font = ImageFont.load_default()
            hist_final_draw.text((hist_width//2 - 60, 10), "RGB Histogram", fill=(50, 50, 50), font=font)

            # 图例
            legend_y = 25
            hist_final_draw.rectangle([h_margin_left, legend_y, h_margin_left + 15, legend_y + 10], fill=(200, 60, 60))
            hist_final_draw.text((h_margin_left + 20, legend_y - 2), "R", fill=(200, 60, 60), font=font)

            hist_final_draw.rectangle([h_margin_left + 50, legend_y, h_margin_left + 65, legend_y + 10], fill=(60, 160, 60))
            hist_final_draw.text((h_margin_left + 70, legend_y - 2), "G", fill=(60, 160, 60), font=font)

            hist_final_draw.rectangle([h_margin_left + 100, legend_y, h_margin_left + 115, legend_y + 10], fill=(60, 60, 200))
            hist_final_draw.text((h_margin_left + 120, legend_y - 2), "B", fill=(60, 60, 200), font=font)

        except:
            pass

        # 计算最终合并图像的尺寸
        total_width = max(new_img_width, hist_width) + 40
        total_height = new_img_height + hist_height + 80

        # 创建最终画布
        final_canvas = Image.new('RGB', (total_width, total_height), (250, 250, 250))

        # 计算居中位置
        img_x = (total_width - new_img_width) // 2
        hist_x = (total_width - hist_width) // 2

        # 粘贴原图
        final_canvas.paste(resized_image, (img_x, 20))

        # 粘贴直方图
        final_canvas.paste(hist_final, (hist_x, new_img_height + 60))

        # 添加标题
        final_draw = ImageDraw.Draw(final_canvas)
        try:
            font = ImageFont.load_default()
            title = "Original Image with RGB Histogram Analysis"
            title_bbox = final_draw.textbbox((0, 0), title, font=font)
            title_width = title_bbox[2] - title_bbox[0]
            title_x = (total_width - title_width) // 2
            final_draw.text((title_x, new_img_height + 30), title, fill=(60, 60, 60), font=font)
        except:
            pass

        return final_canvas

    def extract_rgb_simple(image_path):
        """提取图片的RGB三个通道"""
        
        # 检查文件是否存在
        if not os.path.exists(image_path):
            print(f"错误：找不到图片文件 {image_path}")
            return
        
        try:
            # 打开图片
            image = Image.open(image_path)
            print(f"成功读取图片: {image_path}")
            print(f"图片尺寸: {image.size}")
            print(f"图片模式: {image.mode}")
            
            # 转换为RGB模式（如果不是的话）
            if image.mode != 'RGB':
                image = image.convert('RGB')
                print("已转换为RGB模式")
            
            # 分离RGB三个通道
            r_channel, g_channel, b_channel = image.split()

            # 获取通道数据用于直方图计算
            r_data = list(r_channel.getdata())
            g_data = list(g_channel.getdata())
            b_data = list(b_channel.getdata())

            print("正在计算RGB直方图...")

            # 计算各通道直方图
            r_histogram = calculate_histogram(r_data)
            g_histogram = calculate_histogram(g_data)
            b_histogram = calculate_histogram(b_data)

            # 绘制各通道直方图
            r_hist_img = draw_histogram(r_histogram, (255, 0, 0), "Red Channel Histogram")
            g_hist_img = draw_histogram(g_histogram, (0, 255, 0), "Green Channel Histogram")
            b_hist_img = draw_histogram(b_histogram, (0, 0, 255), "Blue Channel Histogram")

            # 创建纯色通道图像用于显示
            # 创建黑色背景
            width, height = image.size
            black = Image.new('RGB', (width, height), (0, 0, 0))
            
            # 创建各通道的彩色显示版本
            r_display = Image.merge('RGB', (r_channel, black.split()[1], black.split()[2]))
            g_display = Image.merge('RGB', (black.split()[0], g_channel, black.split()[2]))
            b_display = Image.merge('RGB', (black.split()[0], black.split()[1], b_channel))
            
            # 保存各个通道
            r_display.save('R_channel.png')
            g_display.save('G_channel.png')
            b_display.save('B_channel.png')
            
            # 创建水平合并图像
            total_width = width * 3
            combined_horizontal = Image.new('RGB', (total_width, height))
            combined_horizontal.paste(r_display, (0, 0))
            combined_horizontal.paste(g_display, (width, 0))
            combined_horizontal.paste(b_display, (width * 2, 0))
            combined_horizontal.save('RGB_horizontal_combined.png')
            
            # 创建垂直合并图像
            total_height = height * 3
            combined_vertical = Image.new('RGB', (width, total_height))
            combined_vertical.paste(r_display, (0, 0))
            combined_vertical.paste(g_display, (0, height))
            combined_vertical.paste(b_display, (0, height * 2))
            combined_vertical.save('RGB_vertical_combined.png')

            # 保存各通道直方图
            r_hist_img.save('R_histogram.png')
            g_hist_img.save('G_histogram.png')
            b_hist_img.save('B_histogram.png')

            # 创建合并的直方图显示
            hist_width, hist_height = r_hist_img.size

            # 水平合并直方图
            combined_hist_horizontal = Image.new('RGB', (hist_width * 3, hist_height), 'white')
            combined_hist_horizontal.paste(r_hist_img, (0, 0))
            combined_hist_horizontal.paste(g_hist_img, (hist_width, 0))
            combined_hist_horizontal.paste(b_hist_img, (hist_width * 2, 0))
            combined_hist_horizontal.save('RGB_histograms_horizontal.png')

            # 垂直合并直方图
            combined_hist_vertical = Image.new('RGB', (hist_width, hist_height * 3), 'white')
            combined_hist_vertical.paste(r_hist_img, (0, 0))
            combined_hist_vertical.paste(g_hist_img, (0, hist_height))
            combined_hist_vertical.paste(b_hist_img, (0, hist_height * 2))
            combined_hist_vertical.save('RGB_histograms_vertical.png')

            # 创建美观的透明叠加直方图
            overlay_width, overlay_height = 1000, 500

            # 创建基础画布
            base_img = Image.new('RGBA', (overlay_width, overlay_height), (248, 249, 250, 255))
            base_draw = ImageDraw.Draw(base_img)

            # 设置边距
            margin_left, margin_right = 80, 60
            margin_top, margin_bottom = 100, 100
            chart_width = overlay_width - margin_left - margin_right
            chart_height = overlay_height - margin_top - margin_bottom

            # 绘制背景网格
            grid_color = (220, 220, 220, 255)
            for i in range(6):
                y = margin_top + (chart_height * i // 5)
                base_draw.line([(margin_left, y), (overlay_width - margin_right, y)], fill=grid_color, width=1)

            for i in range(0, 256, 32):
                x = margin_left + (chart_width * i // 255)
                base_draw.line([(x, margin_top), (x, overlay_height - margin_bottom)], fill=grid_color, width=1)

            # 找到全局最大值用于归一化
            max_r = max(r_histogram) if max(r_histogram) > 0 else 1
            max_g = max(g_histogram) if max(g_histogram) > 0 else 1
            max_b = max(b_histogram) if max(b_histogram) > 0 else 1
            global_max = max(max_r, max_g, max_b)

            # 为每个通道创建透明图层
            r_layer = Image.new('RGBA', (overlay_width, overlay_height), (0, 0, 0, 0))
            g_layer = Image.new('RGBA', (overlay_width, overlay_height), (0, 0, 0, 0))
            b_layer = Image.new('RGBA', (overlay_width, overlay_height), (0, 0, 0, 0))

            r_draw = ImageDraw.Draw(r_layer)
            g_draw = ImageDraw.Draw(g_layer)
            b_draw = ImageDraw.Draw(b_layer)

            # 绘制半透明的柱状图
            bar_width = chart_width / 256
            alpha = 120  # 透明度 (0-255)

            for i in range(256):
                x1 = margin_left + int(i * bar_width)
                x2 = margin_left + int((i + 1) * bar_width) - 1
                y_base = overlay_height - margin_bottom

                # 红色通道
                if r_histogram[i] > 0:
                    r_height = int((r_histogram[i] / global_max) * chart_height)
                    y_top = y_base - r_height
                    r_draw.rectangle([x1, y_top, x2, y_base], fill=(255, 80, 80, alpha))

                # 绿色通道
                if g_histogram[i] > 0:
                    g_height = int((g_histogram[i] / global_max) * chart_height)
                    y_top = y_base - g_height
                    g_draw.rectangle([x1, y_top, x2, y_base], fill=(80, 255, 80, alpha))

                # 蓝色通道
                if b_histogram[i] > 0:
                    b_height = int((b_histogram[i] / global_max) * chart_height)
                    y_top = y_base - b_height
                    b_draw.rectangle([x1, y_top, x2, y_base], fill=(80, 80, 255, alpha))

            # 合并所有图层
            combined = Image.alpha_composite(base_img, r_layer)
            combined = Image.alpha_composite(combined, g_layer)
            combined = Image.alpha_composite(combined, b_layer)

            # 转换为RGB并添加边框和标注
            final_img = combined.convert('RGB')
            final_draw = ImageDraw.Draw(final_img)

            # 绘制坐标轴
            axis_color = (80, 80, 80)
            # X轴
            final_draw.line([(margin_left, overlay_height - margin_bottom),
                           (overlay_width - margin_right, overlay_height - margin_bottom)],
                          fill=axis_color, width=3)
            # Y轴
            final_draw.line([(margin_left, margin_top),
                           (margin_left, overlay_height - margin_bottom)],
                          fill=axis_color, width=3)

            # 添加轮廓线增强效果
            for i in range(255):
                x1 = margin_left + int(i * bar_width)
                x2 = margin_left + int((i + 1) * bar_width)

                # 红色轮廓
                r_y1 = overlay_height - margin_bottom - int((r_histogram[i] / global_max) * chart_height)
                r_y2 = overlay_height - margin_bottom - int((r_histogram[i+1] / global_max) * chart_height)
                final_draw.line([(x1, r_y1), (x2, r_y2)], fill=(200, 0, 0), width=2)

                # 绿色轮廓
                g_y1 = overlay_height - margin_bottom - int((g_histogram[i] / global_max) * chart_height)
                g_y2 = overlay_height - margin_bottom - int((g_histogram[i+1] / global_max) * chart_height)
                final_draw.line([(x1, g_y1), (x2, g_y2)], fill=(0, 150, 0), width=2)

                # 蓝色轮廓
                b_y1 = overlay_height - margin_bottom - int((b_histogram[i] / global_max) * chart_height)
                b_y2 = overlay_height - margin_bottom - int((b_histogram[i+1] / global_max) * chart_height)
                final_draw.line([(x1, b_y1), (x2, b_y2)], fill=(0, 0, 200), width=2)

            # 添加标题和图例
            try:
                font = ImageFont.load_default()
                # 主标题
                title = "RGB Channels Combined Histogram"
                title_bbox = final_draw.textbbox((0, 0), title, font=font)
                title_width = title_bbox[2] - title_bbox[0]
                title_x = (overlay_width - title_width) // 2
                final_draw.text((title_x, 20), title, fill=(40, 40, 40), font=font)

                # 副标题
                subtitle = "Semi-transparent overlapping distribution"
                subtitle_bbox = final_draw.textbbox((0, 0), subtitle, font=font)
                subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
                subtitle_x = (overlay_width - subtitle_width) // 2
                final_draw.text((subtitle_x, 45), subtitle, fill=(100, 100, 100), font=font)

                # 图例背景
                legend_bg_x1, legend_bg_y1 = margin_left, 70
                legend_bg_x2, legend_bg_y2 = overlay_width - margin_right, 95
                final_draw.rectangle([legend_bg_x1, legend_bg_y1, legend_bg_x2, legend_bg_y2],
                                   fill=(255, 255, 255), outline=(200, 200, 200), width=1)

                # 图例项
                legend_y = 75
                legend_spacing = 120

                # 红色图例
                final_draw.rectangle([margin_left + 10, legend_y, margin_left + 30, legend_y + 15],
                                   fill=(255, 80, 80), outline=(200, 0, 0), width=1)
                final_draw.text((margin_left + 35, legend_y + 2), "Red Channel", fill=(40, 40, 40), font=font)

                # 绿色图例
                final_draw.rectangle([margin_left + 10 + legend_spacing, legend_y,
                                    margin_left + 30 + legend_spacing, legend_y + 15],
                                   fill=(80, 255, 80), outline=(0, 150, 0), width=1)
                final_draw.text((margin_left + 35 + legend_spacing, legend_y + 2), "Green Channel", fill=(40, 40, 40), font=font)

                # 蓝色图例
                final_draw.rectangle([margin_left + 10 + 2*legend_spacing, legend_y,
                                    margin_left + 30 + 2*legend_spacing, legend_y + 15],
                                   fill=(80, 80, 255), outline=(0, 0, 200), width=1)
                final_draw.text((margin_left + 35 + 2*legend_spacing, legend_y + 2), "Blue Channel", fill=(40, 40, 40), font=font)

                # 坐标轴标签
                final_draw.text((margin_left - 5, overlay_height - margin_bottom + 15), '0', fill=axis_color, font=font)
                final_draw.text((overlay_width - margin_right - 15, overlay_height - margin_bottom + 15), '255', fill=axis_color, font=font)
                final_draw.text((overlay_width//2 - 30, overlay_height - margin_bottom + 40), 'Pixel Intensity', fill=axis_color, font=font)

                # Y轴标签
                final_draw.text((15, margin_top - 5), str(global_max), fill=axis_color, font=font)
                final_draw.text((15, overlay_height - margin_bottom - 10), '0', fill=axis_color, font=font)
                final_draw.text((15, overlay_height//2 - 30), 'Frequency', fill=axis_color, font=font)

                # 添加中间刻度
                for i in range(1, 5):
                    y_val = int(global_max * i / 5)
                    y_pos = overlay_height - margin_bottom - (chart_height * i // 5)
                    final_draw.text((15, y_pos - 5), str(y_val), fill=axis_color, font=font)

            except:
                # 简化版标签
                final_draw.text((overlay_width//2 - 100, 20), "RGB Combined Histogram", fill=(40, 40, 40))
                final_draw.text((margin_left, 75), "Red", fill=(200, 0, 0))
                final_draw.text((margin_left + 60, 75), "Green", fill=(0, 150, 0))
                final_draw.text((margin_left + 120, 75), "Blue", fill=(0, 0, 200))

            final_img.save('RGB_histogram_overlay.png')

            # 创建更美观的合并直方图（改进版）
            enhanced_overlay = create_enhanced_histogram(r_histogram, g_histogram, b_histogram)
            enhanced_overlay.save('RGB_histogram_enhanced.png')

            # 创建原图与直方图的合并显示
            combined_with_original = create_image_with_histogram(image, r_histogram, g_histogram, b_histogram)
            combined_with_original.save('Image_with_histogram.png')

            print("\n✅ RGB通道提取和直方图生成完成！")
            print("已保存以下文件：")
            print("\n📸 通道图像：")
            print("- R_channel.png (红色通道)")
            print("- G_channel.png (绿色通道)")
            print("- B_channel.png (蓝色通道)")
            print("- RGB_horizontal_combined.png (水平合并)")
            print("- RGB_vertical_combined.png (垂直合并)")
            print("\n📊 直方图：")
            print("- R_histogram.png (红色通道直方图)")
            print("- G_histogram.png (绿色通道直方图)")
            print("- B_histogram.png (蓝色通道直方图)")
            print("- RGB_histograms_horizontal.png (直方图水平合并)")
            print("- RGB_histograms_vertical.png (直方图垂直合并)")
            print("- RGB_histogram_overlay.png (RGB叠加直方图)")
            print("- RGB_histogram_enhanced.png (增强版叠加直方图)")
            print("- Image_with_histogram.png (原图与直方图合并)")
            
            # 计算统计信息
            total_pixels = width * height
            r_mean = sum(r_data) / total_pixels
            g_mean = sum(g_data) / total_pixels
            b_mean = sum(b_data) / total_pixels

            r_min, r_max = min(r_data), max(r_data)
            g_min, g_max = min(g_data), max(g_data)
            b_min, b_max = min(b_data), max(b_data)

            # 显示统计信息
            print(f"\n📊 图像统计信息：")
            print(f"尺寸: {width} x {height} 像素")
            print(f"总像素数: {total_pixels:,}")
            print(f"\n🔴 红色通道统计：")
            print(f"  平均值: {r_mean:.2f}")
            print(f"  范围: {r_min} - {r_max}")
            print(f"\n🟢 绿色通道统计：")
            print(f"  平均值: {g_mean:.2f}")
            print(f"  范围: {g_min} - {g_max}")
            print(f"\n🔵 蓝色通道统计：")
            print(f"  平均值: {b_mean:.2f}")
            print(f"  范围: {b_min} - {b_max}")
            print(f"\n🎨 整体色彩特征：")
            dominant_channel = "红色" if r_mean > g_mean and r_mean > b_mean else "绿色" if g_mean > b_mean else "蓝色"
            print(f"  主导颜色通道: {dominant_channel}")
            print(f"  RGB平均值: ({r_mean:.1f}, {g_mean:.1f}, {b_mean:.1f})")
            
        except Exception as e:
            print(f"处理图片时出错: {e}")
    
    if __name__ == "__main__":
        # 图片路径
        image_path = "D:/学习/高效视频重建与恢复/数据集/1.png"
        
        print("🎨 RGB图像通道提取器")
        print("=" * 50)
        
        # 提取RGB通道
        extract_rgb_simple(image_path)

except ImportError:
    print("❌ 缺少Pillow库！")
    print("请运行以下命令安装：")
    print("pip install Pillow")
    print("\n或者如果您使用conda：")
    print("conda install pillow")
