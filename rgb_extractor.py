import cv2
import numpy as np
import matplotlib.pyplot as plt

def extract_rgb_channels(image_path):
    """提取图片的RGB三个通道"""
    
    # 读取图片
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图片: {image_path}")
        return
    
    # OpenCV读取的是BGR格式，转换为RGB
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 分离RGB三个通道
    r_channel = image_rgb[:, :, 0]  # 红色通道
    g_channel = image_rgb[:, :, 1]  # 绿色通道  
    b_channel = image_rgb[:, :, 2]  # 蓝色通道
    
    # 创建彩色显示的通道图像
    r_display = np.zeros_like(image_rgb)
    r_display[:, :, 0] = r_channel
    
    g_display = np.zeros_like(image_rgb)
    g_display[:, :, 1] = g_channel
    
    b_display = np.zeros_like(image_rgb)
    b_display[:, :, 2] = b_channel
    
    # 显示结果
    plt.figure(figsize=(15, 10))
    
    # 原图
    plt.subplot(2, 3, 1)
    plt.imshow(image_rgb)
    plt.title('原图')
    plt.axis('off')
    
    # R通道
    plt.subplot(2, 3, 2)
    plt.imshow(r_display)
    plt.title('R通道')
    plt.axis('off')
    
    # G通道
    plt.subplot(2, 3, 3)
    plt.imshow(g_display)
    plt.title('G通道')
    plt.axis('off')
    
    # B通道
    plt.subplot(2, 3, 4)
    plt.imshow(b_display)
    plt.title('B通道')
    plt.axis('off')
    
    # 水平合并显示
    combined_horizontal = np.hstack([r_display, g_display, b_display])
    plt.subplot(2, 3, 5)
    plt.imshow(combined_horizontal)
    plt.title('RGB三通道水平排列')
    plt.axis('off')
    
    # 垂直合并显示
    combined_vertical = np.vstack([r_display, g_display, b_display])
    plt.subplot(2, 3, 6)
    plt.imshow(combined_vertical)
    plt.title('RGB三通道垂直排列')
    plt.axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # 保存结果
    cv2.imwrite('R_channel.png', cv2.cvtColor(r_display, cv2.COLOR_RGB2BGR))
    cv2.imwrite('G_channel.png', cv2.cvtColor(g_display, cv2.COLOR_RGB2BGR))
    cv2.imwrite('B_channel.png', cv2.cvtColor(b_display, cv2.COLOR_RGB2BGR))
    cv2.imwrite('RGB_horizontal.png', cv2.cvtColor(combined_horizontal, cv2.COLOR_RGB2BGR))
    cv2.imwrite('RGB_vertical.png', cv2.cvtColor(combined_vertical, cv2.COLOR_RGB2BGR))
    
    print("RGB通道图像已保存:")
    print("- R_channel.png")
    print("- G_channel.png") 
    print("- B_channel.png")
    print("- RGB_horizontal.png")
    print("- RGB_vertical.png")

if __name__ == "__main__":
    # 图片路径
    image_path = "D:/学习/高效视频重建与恢复/数据集/Rail 2023/1_calibration_1.1/rgb_center/012_1631441453.300000000.png"
    
    # 提取RGB通道
    extract_rgb_channels(image_path)
